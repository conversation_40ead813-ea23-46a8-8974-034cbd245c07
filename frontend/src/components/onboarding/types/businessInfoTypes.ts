export interface MerchantData {
  dba?: string;
  new?: number;
  mcc?: string;
  status?: string;
  annualCCSales?: number;
  avgTicket?: number;
  established?: string;
  members?: Array<MemberData>;
}

export interface MemberData {
  title?: string;
  first?: string;
  middle?: string;
  last?: string;
  ssn?: string;
  dob?: string;
  dl?: string;
  dlstate?: string;
  ownership?: number;
  significantResponsibility?: number;
  politicallyExposed?: number;
  email?: string;
  phone?: string;
  primary?: string;
  address1?: string;
  address2?: string;
  city?: string;
  state?: string;
  zip?: string;
  country?: string;
}

export interface BankVerificationData {
  verificationMethod: "plaid" | "manual";
  verificationFile?: {
    name: string;
    size: number;
    type: string;
    content: string;
  };
  plaidData?: {
    publicToken: string;
    accountToken: string;
    platform: "PLAID";
    institutionName?: string;
    accountName?: string;
    accountMask?: string;
  };
}

export interface BusinessInfoFormData {
  name?: string;
  type?: number;
  ein?: string;
  website?: string;
  email?: string;
  phone?: string;
  customerPhone?: string;
  address1?: string;
  address2?: string;
  city?: string;
  state?: string;
  zip?: string;
  country?: string;
  public?: number;
  merchant?: MerchantData;
  bankVerification?: BankVerificationData;
  createAccount?: boolean;
  username?: string;
  password?: string;
  confirmPassword?: string;
  [key: string]: any;
}

export interface ValidationErrors {
  [key: string]: string;
}

export interface FieldUpdate {
  [key: string]: string | number | object;
}

export interface BusinessTypeOption {
  value: number;
  label: string;
}

export interface EntityClassificationOption {
  value: number;
  label: string;
}

export interface MCCCodeOption {
  code: string;
  description: string;
}

export interface FormSectionProps {
  formData: BusinessInfoFormData;
  errors: ValidationErrors;
  onChange: (field: string, value: string | number) => void;
}

export interface UseBusinessInfoFormReturn {
  formData: BusinessInfoFormData;
  errors: ValidationErrors;
  selectedBusinessType: number | undefined;
  requiresCorporateStructure: boolean;
  handleSubmit: (e: React.FormEvent) => void;
  handleChange: (field: string, value: string | number) => void;
  handleFillDemoData: () => void;
}

export interface DemoDataConfig {
  includeAccountCreation?: boolean;
  includeBankVerification?: boolean;
  customOverrides?: Record<string, any>;
}

export interface BusinessTypeChecks {
  isSoleProprietor: (type: number) => boolean;
  isCorporation: (type: number) => boolean;
  isLLC: (type: number) => boolean;
  isPartnership: (type: number) => boolean;
  isAssociation: (type: number) => boolean;
  isNonProfit: (type: number) => boolean;
  isGovernment: (type: number) => boolean;
  isCCorporation: (type: number) => boolean;
  isSCorporation: (type: number) => boolean;
  requiresCorporateStructure: (type: number) => boolean;
  isPublicEntity: (type: number) => boolean;
  isPrivateEntity: (type: number) => boolean;
}
