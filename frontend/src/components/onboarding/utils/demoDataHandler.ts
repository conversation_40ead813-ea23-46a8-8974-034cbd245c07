import { DEMO_MERCHANT_DATA } from "../../../redux/slices/onboardingSlice";

export interface DemoDataConfig {
  includeAccountCreation?: boolean;
  includeBankVerification?: boolean;
  customOverrides?: Record<string, any>;
}

export const getDemoData = (config: DemoDataConfig = {}) => {
  const {
    includeAccountCreation = true,
    includeBankVerification = false,
    customOverrides = {},
  } = config;

  let demoData = { ...DEMO_MERCHANT_DATA };

  if (!includeAccountCreation) {
    delete demoData.createAccount;
    delete demoData.username;
    delete demoData.password;
    delete demoData.confirmPassword;
  }

  if (includeBankVerification) {
    demoData = {
      ...demoData,
      bankVerification: {
        verificationMethod: "manual" as const,
        verificationFile: {
          name: "demo-void-check.pdf",
          size: 1024000,
          type: "application/pdf",
          content: "data:application/pdf;base64,JVBERi0xLjQKJdPr6eEKMSAwIG9iago8PAovVHlwZSAvQ2F0YWxvZwovUGFnZXMgMiAwIFIKPj4KZW5kb2JqCjIgMCBvYmoKPDwKL1R5cGUgL1BhZ2VzCi9LaWRzIFszIDAgUl0KL0NvdW50IDEKPD4KZW5kb2JqCjMgMCBvYmoKPDwKL1R5cGUgL1BhZ2UKL1BhcmVudCAyIDAgUgovTWVkaWFCb3ggWzAgMCA2MTIgNzkyXQovUmVzb3VyY2VzIDw8Ci9Gb250IDw8Ci9GMSA0IDAgUgo+Pgo+PgovQ29udGVudHMgNSAwIFIKPj4KZW5kb2JqCjQgMCBvYmoKPDwKL1R5cGUgL0ZvbnQKL1N1YnR5cGUgL1R5cGUxCi9CYXNlRm9udCAvSGVsdmV0aWNhCj4+CmVuZG9iago1IDAgb2JqCjw8Ci9MZW5ndGggNDQKPj4Kc3RyZWFtCkJUCi9GMSAxMiBUZgoxMDAgNzAwIFRkCihEZW1vIFZvaWQgQ2hlY2spIFRqCkVUCmVuZHN0cmVhbQplbmRvYmoKeHJlZgowIDYKMDAwMDAwMDAwMCA2NTUzNSBmIAowMDAwMDAwMDA5IDAwMDAwIG4gCjAwMDAwMDAwNTggMDAwMDAgbiAKMDAwMDAwMDExNSAwMDAwMCBuIAowMDAwMDAwMjQ1IDAwMDAwIG4gCjAwMDAwMDAzMjIgMDAwMDAgbiAKdHJhaWxlcgo8PAovU2l6ZSA2Ci9Sb290IDEgMCBSCj4+CnN0YXJ0eHJlZgo0MTYKJSVFT0Y=",
        },
      },
    };
  }

  return {
    ...demoData,
    ...customOverrides,
  };
};

export const getBusinessInfoDemoData = () => {
  return getDemoData({
    includeAccountCreation: false,
    includeBankVerification: false,
  });
};

export const getFullDemoData = () => {
  return getDemoData({
    includeAccountCreation: true,
    includeBankVerification: true,
  });
};

export const getCustomDemoData = (overrides: Record<string, any>) => {
  return getDemoData({
    customOverrides: overrides,
  });
};
